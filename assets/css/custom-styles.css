/*
 * Custom Styles for Realome Theme
 * Additional CSS styles and effects
 */

/* ========================================
   CSS CUSTOM PROPERTIES (VARIABLES)
   ======================================== */

:root {
    /* Gravity Forms Button Colors */
    --gform-submit-bg: #2f5d50;
    --gform-submit-bg-hover: #2f5d50;
    --gform-submit-text: #ffffff;

    /* Additional color variables for easy customization */
    --primary-shadow: rgba(0, 0, 0, 0.15);
    --hover-shadow: rgba(0, 0, 0, 0.25);
}

/* ========================================
   SMOOTH IMAGE ZOOM EFFECTS
   ======================================== */

/* Basic image zoom on hover */
.zoom-effect img,
.wp-block-image img,
.wp-block-media-text__media img,
.wp-block-cover img,
.post-thumbnail img,
.attachment-post-thumbnail,
.gallery img {
    transition: transform 0.4s ease, filter 0.4s ease;
    transform: scale(1);
    cursor: pointer;
}

.zoom-effect:hover img,
.wp-block-image:hover img,
.wp-block-media-text__media:hover img,
.wp-block-cover:hover img,
.post-thumbnail:hover img,
.attachment-post-thumbnail:hover,
.gallery:hover img {
    transform: scale(1.04);
}

/* Zoom effect with overlay container */
.zoom-container {
    overflow: hidden;
    position: relative;
    border-radius: 8px;
}

.zoom-container img {
    transition: transform 0.5s ease;
    transform: scale(1);
    width: 100%;
    height: auto;
    display: block;
}

.zoom-container:hover img {
    transform: scale(1.15);
}

/* Zoom effect with subtle overlay */
.zoom-overlay {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
}

.zoom-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 1;
    pointer-events: none;
}

.zoom-overlay:hover::before {
    opacity: 1;
}

.zoom-overlay img {
    transition: transform 0.4s ease;
    transform: scale(1);
}

.zoom-overlay:hover img {
    transform: scale(1.02);
}

/* Smooth zoom for gallery images */
.wp-block-gallery .wp-block-image {
    overflow: hidden;
    border-radius: 8px;
}

.wp-block-gallery .wp-block-image img {
    transition: transform 0.3s ease;
}

.wp-block-gallery .wp-block-image:hover img {
    transform: scale(1.02);
}

/* Featured image zoom effect */
.wp-block-post-featured-image {
    overflow: hidden;
    border-radius: 8px;
}

.wp-block-post-featured-image img {
    transition: transform 0.4s ease;
}

.wp-block-post-featured-image:hover img {
    transform: scale(1.04);
}

/* ========================================
   CUSTOM BOX EFFECTS
   ======================================== */

/* Smooth drop-shadow hover effect for boxes */
.smooth-shadow-hover {
    transition: all 0.4s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    transform: scale(1);
}

.smooth-shadow-hover:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px) scale(1.02);
}

/* Enhanced shadow effect */
.enhanced-shadow-hover {
    transition: all 0.5s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    transform: scale(1);
}

.enhanced-shadow-hover:hover {
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
    transform: translateY(-4px) scale(1.03);
}

/* Subtle shadow effect */
.subtle-shadow-hover {
    transition: all 0.3s ease;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    transform: scale(1);
}

.subtle-shadow-hover:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px) scale(1.01);
}

/* Colored shadow effects */
.blue-shadow-hover {
    transition: all 0.4s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    transform: scale(1);
}

.blue-shadow-hover:hover {
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
    transform: translateY(-2px) scale(1.02);
}

.green-shadow-hover {
    transition: all 0.4s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    transform: scale(1);
}

.green-shadow-hover:hover {
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
    transform: translateY(-2px) scale(1.02);
}

.orange-shadow-hover {
    transition: all 0.4s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    transform: scale(1);
}

.orange-shadow-hover:hover {
    box-shadow: 0 8px 25px rgba(249, 115, 22, 0.3);
    transform: translateY(-2px) scale(1.02);
}

/* ========================================
   MAGI-SQUARE HOVER EFFECTS
   ======================================== */

/* Magi-square path hover effects */
.magi-square path,
.magi-square-path {
    transition: fill 0.3s ease, stroke 0.3s ease, opacity 0.3s ease;
    cursor: pointer;
}

/* Green fill on hover */
.magi-square path:hover,
.magi-square-path:hover {
    fill: #22c55e !important; /* Green color */
    stroke: #16a34a; /* Darker green stroke */
    opacity: 0.9;
}

/* Alternative green shades for different elements */
.magi-square path.light-green:hover,
.magi-square-path.light-green:hover {
    fill: #4ade80 !important; /* Light green */
    stroke: #22c55e;
}

.magi-square path.dark-green:hover,
.magi-square-path.dark-green:hover {
    fill: #16a34a !important; /* Dark green */
    stroke: #15803d;
}

/* Smooth animation for SVG containers */
.magi-square {
    transition: transform 0.2s ease;
}

.magi-square:hover {
    transform: scale(1.02);
}

/* For grouped paths - hover effect on parent affects all children */
.magi-square-group:hover path,
.magi-square-group:hover .magi-square-path {
    fill: #22c55e !important;
    stroke: #16a34a;
    opacity: 0.9;
}

/* Glow effect for enhanced visual feedback */
.magi-square path.glow-effect:hover,
.magi-square-path.glow-effect:hover {
    filter: drop-shadow(0 0 8px #22c55e);
}

/* ========================================
   UTILITY CLASSES
   ======================================== */

/* Disable zoom effect */
.no-zoom img {
    transition: none !important;
    transform: none !important;
}

.no-zoom:hover img {
    transform: none !important;
}

/* Enhanced zoom effect */
.enhanced-zoom img {
    transition: transform 0.6s ease, filter 0.6s ease;
}

.enhanced-zoom:hover img {
    transform: scale(1.2);
    filter: brightness(1.1) contrast(1.05);
}

/* Subtle zoom effect */
.subtle-zoom img {
    transition: transform 0.3s ease;
}

.subtle-zoom:hover img {
    transform: scale(1.03);
}

/* ========================================
   RESPONSIVE ADJUSTMENTS
   ======================================== */

/* Disable zoom effects on mobile for performance */
@media (max-width: 768px) {
    .zoom-effect img,
    .wp-block-image img,
    .wp-block-media-text__media img,
    .wp-block-cover img,
    .post-thumbnail img,
    .attachment-post-thumbnail,
    .gallery img,
    .zoom-container img,
    .zoom-overlay img,
    .wp-block-gallery .wp-block-image img,
    .wp-block-post-featured-image img {
        transition: none;
    }
    
    .zoom-effect:hover img,
    .wp-block-image:hover img,
    .wp-block-media-text__media:hover img,
    .wp-block-cover:hover img,
    .post-thumbnail:hover img,
    .attachment-post-thumbnail:hover,
    .gallery:hover img,
    .zoom-container:hover img,
    .zoom-overlay:hover img,
    .wp-block-gallery .wp-block-image:hover img,
    .wp-block-post-featured-image:hover img {
        transform: none;
    }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    .zoom-effect img,
    .wp-block-image img,
    .wp-block-media-text__media img,
    .wp-block-cover img,
    .post-thumbnail img,
    .attachment-post-thumbnail,
    .gallery img,
    .zoom-container img,
    .zoom-overlay img,
    .wp-block-gallery .wp-block-image img,
    .wp-block-post-featured-image img,
    .enhanced-zoom img,
    .subtle-zoom img {
        transition: none !important;
    }
    
    .zoom-effect:hover img,
    .wp-block-image:hover img,
    .wp-block-media-text__media:hover img,
    .wp-block-cover:hover img,
    .post-thumbnail:hover img,
    .attachment-post-thumbnail:hover,
    .gallery:hover img,
    .zoom-container:hover img,
    .zoom-overlay:hover img,
    .wp-block-gallery .wp-block-image:hover img,
    .wp-block-post-featured-image:hover img,
    .enhanced-zoom:hover img,
    .subtle-zoom:hover img {
        transform: none !important;
    }
}
