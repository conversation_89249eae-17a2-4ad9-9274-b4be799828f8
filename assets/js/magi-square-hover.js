/**
 * Magi-Square Hover Effects
 * 
 * Creates interactive hover effects for magi-square SVG paths
 * Fills paths with green color on mouse hover
 */

// Configuration
const MAGI_SQUARE_CONFIG = {
    defaultFillColor: '#22c55e',      // Green
    defaultStrokeColor: '#16a34a',    // Darker green
    lightGreenFill: '#4ade80',        // Light green
    lightGreenStroke: '#22c55e',      // Green
    darkGreenFill: '#16a34a',         // Dark green
    darkGreenStroke: '#15803d',       // Darker green
    hoverOpacity: 0.9,
    transitionDuration: '0.3s',
    enabled: true
};

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    if (MAGI_SQUARE_CONFIG.enabled) {
        initMagiSquareHoverEffects();
        // Specifically target the magikas SVG
        initMagikasSVGHover();
    }
});

/**
 * Initialize hover effects for magi-square elements
 */
function initMagiSquareHoverEffects() {
    
    // Find all magi-square elements
    const magiSquareElements = document.querySelectorAll('.magi-square, .magi-square-path, .magi-square-group');
    
    magiSquareElements.forEach(element => {
        setupMagiSquareHover(element);
    });
    
    // Also setup for any paths within magi-square containers
    const magiSquarePaths = document.querySelectorAll('.magi-square path, .magi-square-group path');
    
    magiSquarePaths.forEach(path => {
        setupPathHover(path);
    });
}

/**
 * Setup hover effects for magi-square containers
 */
function setupMagiSquareHover(element) {
    
    // Store original styles
    const paths = element.querySelectorAll('path');
    const originalStyles = new Map();
    
    paths.forEach(path => {
        originalStyles.set(path, {
            fill: path.getAttribute('fill') || getComputedStyle(path).fill,
            stroke: path.getAttribute('stroke') || getComputedStyle(path).stroke,
            opacity: path.getAttribute('opacity') || getComputedStyle(path).opacity
        });
    });
    
    // Mouse enter event
    element.addEventListener('mouseenter', function() {
        paths.forEach(path => {
            applyHoverStyles(path);
        });
    });
    
    // Mouse leave event
    element.addEventListener('mouseleave', function() {
        paths.forEach(path => {
            const original = originalStyles.get(path);
            if (original) {
                restoreOriginalStyles(path, original);
            }
        });
    });
}

/**
 * Setup hover effects for individual paths
 */
function setupPathHover(path) {
    
    // Store original styles
    const originalStyles = {
        fill: path.getAttribute('fill') || getComputedStyle(path).fill,
        stroke: path.getAttribute('stroke') || getComputedStyle(path).stroke,
        opacity: path.getAttribute('opacity') || getComputedStyle(path).opacity
    };
    
    // Mouse enter event
    path.addEventListener('mouseenter', function() {
        applyHoverStyles(path);
    });
    
    // Mouse leave event
    path.addEventListener('mouseleave', function() {
        restoreOriginalStyles(path, originalStyles);
    });
}

/**
 * Apply hover styles to a path
 */
function applyHoverStyles(path) {
    
    // Determine which green variant to use
    let fillColor = MAGI_SQUARE_CONFIG.defaultFillColor;
    let strokeColor = MAGI_SQUARE_CONFIG.defaultStrokeColor;
    
    if (path.classList.contains('light-green')) {
        fillColor = MAGI_SQUARE_CONFIG.lightGreenFill;
        strokeColor = MAGI_SQUARE_CONFIG.lightGreenStroke;
    } else if (path.classList.contains('dark-green')) {
        fillColor = MAGI_SQUARE_CONFIG.darkGreenFill;
        strokeColor = MAGI_SQUARE_CONFIG.darkGreenStroke;
    }
    
    // Apply hover styles
    path.style.fill = fillColor;
    path.style.stroke = strokeColor;
    path.style.opacity = MAGI_SQUARE_CONFIG.hoverOpacity;
    path.style.transition = `fill ${MAGI_SQUARE_CONFIG.transitionDuration} ease, stroke ${MAGI_SQUARE_CONFIG.transitionDuration} ease, opacity ${MAGI_SQUARE_CONFIG.transitionDuration} ease`;
    
    // Add glow effect if specified
    if (path.classList.contains('glow-effect')) {
        path.style.filter = `drop-shadow(0 0 8px ${fillColor})`;
    }
}

/**
 * Restore original styles to a path
 */
function restoreOriginalStyles(path, originalStyles) {
    path.style.fill = originalStyles.fill;
    path.style.stroke = originalStyles.stroke;
    path.style.opacity = originalStyles.opacity;
    
    // Remove glow effect
    if (path.classList.contains('glow-effect')) {
        path.style.filter = 'none';
    }
}

/**
 * Initialize hover effects specifically for the magikas SVG
 */
function initMagikasSVGHover() {
    const magikasSVG = document.getElementById('magikas');

    if (magikasSVG) {
        // Add magi-square class to the SVG
        magikasSVG.classList.add('magi-square');

        // Setup hover effects for all paths within the magikas SVG
        const paths = magikasSVG.querySelectorAll('path');

        paths.forEach(path => {
            setupPathHover(path);
        });

        console.log(`Magi-square hover effects applied to ${paths.length} paths in #magikas SVG`);
    } else {
        // If not found immediately, try again after a short delay (for dynamic content)
        setTimeout(() => {
            const delayedMagikasSVG = document.getElementById('magikas');
            if (delayedMagikasSVG) {
                delayedMagikasSVG.classList.add('magi-square');
                const delayedPaths = delayedMagikasSVG.querySelectorAll('path');
                delayedPaths.forEach(path => {
                    setupPathHover(path);
                });
                console.log(`Magi-square hover effects applied to ${delayedPaths.length} paths in #magikas SVG (delayed)`);
            }
        }, 1000);
    }
}

/**
 * Add magi-square class to existing SVG elements
 * Call this function to convert existing SVGs to magi-squares
 */
function convertToMagiSquare(selector) {
    const elements = document.querySelectorAll(selector);

    elements.forEach(element => {
        element.classList.add('magi-square');
        setupMagiSquareHover(element);
    });
}

/**
 * Add magi-square-path class to existing path elements
 * Call this function to convert existing paths to magi-square paths
 */
function convertPathsToMagiSquare(selector) {
    const paths = document.querySelectorAll(selector);
    
    paths.forEach(path => {
        path.classList.add('magi-square-path');
        setupPathHover(path);
    });
}

/**
 * Re-initialize when new content is loaded (for dynamic content)
 */
if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        const newMagiSquares = node.querySelectorAll('.magi-square:not(.magi-hover-processed), .magi-square-path:not(.magi-hover-processed), .magi-square-group:not(.magi-hover-processed)');
                        if (newMagiSquares.length > 0) {
                            setTimeout(initMagiSquareHoverEffects, 100);
                        }
                    }
                });
            }
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

// Export functions for global use
window.MagiSquareHover = {
    init: initMagiSquareHoverEffects,
    initMagikas: initMagikasSVGHover,
    convertToMagiSquare: convertToMagiSquare,
    convertPathsToMagiSquare: convertPathsToMagiSquare,
    config: MAGI_SQUARE_CONFIG
};
